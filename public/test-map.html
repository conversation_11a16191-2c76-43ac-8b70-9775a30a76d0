<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D中国地图测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts-gl@2.0.9/dist/echarts-gl.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0c1426 100%);
            font-family: 'Arial', sans-serif;
            color: white;
        }
        
        .container {
            width: 100%;
            height: 80vh;
            position: relative;
            background: 
                radial-gradient(circle at 20% 20%, rgba(64, 224, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 100, 150, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0c1426 100%);
            border: 1px solid rgba(64, 224, 255, 0.3);
            border-radius: 12px;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(64, 224, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(64, 224, 255, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            pointer-events: none;
            opacity: 0.3;
            z-index: 1;
        }
        
        #map-container {
            width: 100%;
            height: 100%;
            position: relative;
            z-index: 10;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #40e0ff;
            font-size: 18px;
            z-index: 100;
        }
        
        .info {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(0, 20, 40, 0.8);
            border: 1px solid #40e0ff;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="info">
        <h2>3D中国地图测试页面</h2>
        <p>测试地图数据加载和3D渲染功能</p>
        <div id="status">正在初始化...</div>
    </div>
    
    <div class="container">
        <div class="loading" id="loading">正在加载地图数据...</div>
        <div id="map-container"></div>
    </div>

    <script>
        // 城市数据
        const cityData = [
            { name: '成都', coord: [104.066, 30.572], value: 85, status: 'online' },
            { name: '石家庄', coord: [114.514, 38.042], value: 72, status: 'building' },
            { name: '北京', coord: [116.405, 39.905], value: 95, status: 'online' },
            { name: '上海', coord: [121.473, 31.23], value: 88, status: 'online' },
            { name: '广州', coord: [113.264, 23.129], value: 76, status: 'building' },
            { name: '深圳', coord: [114.057, 22.543], value: 82, status: 'online' }
        ];

        let myChart;
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        // 初始化图表
        function initChart() {
            const container = document.getElementById('map-container');
            myChart = echarts.init(container);
            updateStatus('图表实例创建成功');
            
            // 尝试多个地图数据源
            loadMapData();
        }

        async function loadMapData() {
            const mapUrls = [
                '/json/100000_full.json',
                'https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json'
            ];

            for (const url of mapUrls) {
                try {
                    updateStatus(`尝试加载: ${url}`);
                    const response = await fetch(url);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }

                    const geoJson = await response.json();
                    
                    if (geoJson && (geoJson.features || geoJson.geometries)) {
                        updateStatus(`地图数据加载成功: ${url}`);
                        echarts.registerMap('china', geoJson);
                        renderChart();
                        document.getElementById('loading').style.display = 'none';
                        return;
                    } else {
                        throw new Error('数据格式不正确');
                    }
                } catch (error) {
                    updateStatus(`${url} 加载失败: ${error.message}`);
                    console.warn(`地图数据源 ${url} 加载失败:`, error);
                }
            }
            
            updateStatus('所有地图数据源加载失败，使用备用方案');
            renderFallbackChart();
            document.getElementById('loading').style.display = 'none';
        }

        // 渲染3D地图
        function renderChart() {
            const option = {
                backgroundColor: 'transparent',
                tooltip: {
                    trigger: 'item',
                    backgroundColor: 'rgba(0, 20, 40, 0.95)',
                    borderColor: '#40e0ff',
                    borderWidth: 1,
                    borderRadius: 8,
                    textStyle: { color: '#fff', fontSize: 14 },
                    padding: 12,
                    formatter: function (params) {
                        if (params.seriesType === 'scatter3D') {
                            const city = cityData.find(c => c.name === params.name);
                            const statusColor = city?.status === 'online' ? '#40e0ff' : '#ff6b35';
                            return `<div style="padding: 8px;">
                                <div style="font-weight: bold; margin-bottom: 8px; color: ${statusColor};">${params.name}</div>
                                <div>活跃度: <span style="color: #40e0ff;">${city?.value || 0}</span></div>
                                <div>状态: <span style="color: ${statusColor};">${city?.status === 'online' ? '正常运行' : '建设中'}</span></div>
                            </div>`;
                        }
                        return params.name;
                    }
                },
                geo3D: {
                    map: 'china',
                    regionHeight: 8,
                    roam: true,
                    itemStyle: {
                        color: function() {
                            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: '#2563eb' },
                                { offset: 1, color: '#1e40af' }
                            ]);
                        },
                        opacity: 0.85,
                        borderWidth: 2,
                        borderColor: '#40e0ff'
                    },
                    emphasis: {
                        itemStyle: {
                            color: '#3b82f6',
                            borderColor: '#80ff80',
                            borderWidth: 3
                        }
                    },
                    light: {
                        main: {
                            color: '#ffffff',
                            intensity: 1.5,
                            shadow: true,
                            shadowQuality: 'high',
                            alpha: 25,
                            beta: 45
                        },
                        ambient: {
                            color: '#40e0ff',
                            intensity: 0.5
                        }
                    },
                    viewControl: {
                        autoRotate: false,
                        distance: 120,
                        alpha: 30,
                        beta: 0,
                        animation: true,
                        animationDurationUpdate: 1000,
                        damping: 0.8
                    },
                    groundPlane: {
                        show: true,
                        color: '#0a1428'
                    },
                    environment: '#0a1428',
                    postEffect: {
                        enable: true,
                        bloom: {
                            enable: true,
                            intensity: 0.3
                        },
                        SSAO: {
                            enable: true,
                            radius: 2
                        }
                    }
                },
                series: [
                    {
                        type: 'map3D',
                        map: 'china',
                        regionHeight: 8,
                        itemStyle: {
                            color: function() {
                                return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    { offset: 0, color: '#2563eb' },
                                    { offset: 1, color: '#1e40af' }
                                ]);
                            },
                            opacity: 0.85,
                            borderWidth: 2,
                            borderColor: '#40e0ff'
                        }
                    },
                    {
                        type: 'scatter3D',
                        coordinateSystem: 'geo3D',
                        data: cityData.map(city => ({
                            name: city.name,
                            value: [...city.coord, 15],
                            itemStyle: {
                                color: city.status === 'online' ? '#40e0ff' : '#ff6b35',
                                opacity: 0.9
                            }
                        })),
                        symbol: 'circle',
                        symbolSize: 15,
                        itemStyle: {
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        },
                        label: {
                            show: true,
                            position: 'top',
                            color: '#ffffff',
                            fontSize: 12,
                            fontWeight: 'bold',
                            backgroundColor: 'rgba(0,0,0,0.6)',
                            padding: [4, 8],
                            borderRadius: 4
                        }
                    }
                ],
                animation: true,
                animationDuration: 2000,
                animationEasing: 'cubicOut'
            };

            myChart.setOption(option);
            updateStatus('3D地图渲染成功');
        }

        // 备用方案
        function renderFallbackChart() {
            const option = {
                backgroundColor: 'transparent',
                tooltip: {
                    trigger: 'item',
                    backgroundColor: 'rgba(0, 20, 40, 0.95)',
                    borderColor: '#40e0ff',
                    textStyle: { color: '#fff' }
                },
                series: [{
                    type: 'scatter',
                    coordinateSystem: 'cartesian2d',
                    data: cityData.map(city => ({
                        name: city.name,
                        value: [city.coord[0] - 100, city.coord[1] - 30, city.value],
                        itemStyle: {
                            color: city.status === 'online' ? '#40e0ff' : '#ff6b35'
                        }
                    })),
                    symbolSize: 20,
                    label: {
                        show: true,
                        position: 'top',
                        color: '#ffffff'
                    }
                }],
                xAxis: { show: false },
                yAxis: { show: false },
                grid: { left: 0, right: 0, top: 0, bottom: 0 }
            };

            myChart.setOption(option);
            updateStatus('使用备用2D散点图');
        }

        // 响应式处理
        window.addEventListener('resize', function() {
            if (myChart) {
                myChart.resize();
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
        });
    </script>
</body>
</html>
