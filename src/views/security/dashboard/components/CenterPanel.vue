<template>
  <div class="center-panel">
    <!-- 中国3D地图 -->
    <div class="map-container">
      <div class="map-header">
        <h3 class="map-title">全国分布概览</h3>
      </div>
      <div ref="mapChartRef" class="map-chart"></div>
    </div>

    <!-- 关键指标概览 -->
    <div class="metrics-container">
      <div class="metrics-grid">
        <div
          v-for="(metric, index) in metricsData"
          :key="index"
          class="metric-card"
          :style="{ background: metric.background }"
        >
          <div class="metric-content">
            <div class="metric-header">
              <span class="metric-title">{{ metric.title }}</span>
              <Icon :icon="metric.icon" :size="24" class="metric-icon" />
            </div>
            <div class="metric-value">{{ metric.value }}</div>
            <div class="metric-change" :style="{ color: metric.changeColor }">
              <Icon :icon="metric.changeIcon" :size="14" />
              <span>{{ metric.change }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import echarts from '@/utils/lib/echarts';

  const mapChartRef = ref();
  const { setOptions: setMapOptions, getInstance } = useECharts(mapChartRef);

  // 关键指标数据
  const metricsData = ref([
    {
      title: '今日活跃次数',
      value: '12',
      change: '+8%',
      changeColor: '#E67E22',
      changeIcon: 'ant-design:arrow-up-outlined',
      background: 'linear-gradient(135deg, #E74C3C 0%, #C0392B 100%)',
      icon: 'ant-design:fire-outlined',
    },
    {
      title: '今日作业任务',
      value: '47',
      change: '+8%',
      changeColor: '#3498DB',
      changeIcon: 'ant-design:arrow-up-outlined',
      background: 'linear-gradient(135deg, #26B99A 0%, #1ABC9C 100%)',
      icon: 'ant-design:tool-outlined',
    },
    {
      title: 'AR眼镜使用次数',
      value: '32',
      change: '+5%',
      changeColor: '#5DADE2',
      changeIcon: 'ant-design:arrow-up-outlined',
      background: 'linear-gradient(135deg, #3498DB 0%, #2980B9 100%)',
      icon: 'ant-design:eye-outlined',
    },
    {
      title: '工程车辆检修',
      value: '8',
      change: '30%',
      changeColor: '#95A5A6',
      changeIcon: 'ant-design:percentage-outlined',
      background: 'linear-gradient(135deg, #8E44AD 0%, #7D3C98 100%)',
      icon: 'ant-design:car-outlined',
    },
  ]);

  // 地图数据
  const mapData = [
    { name: '成都', value: [104.066, 30.572, 85], itemStyle: { color: '#26B99A' } },
    { name: '石家庄', value: [114.514, 38.042, 72], itemStyle: { color: '#3498DB' } },
  ];

  // 初始化地图
  function initMap() {
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: { color: '#fff' },
        formatter: function (params) {
          if (params.seriesType === 'scatter') {
            return `${params.name}<br/>活跃度: ${params.value[2]}`;
          }
          return params.name;
        },
      },
      geo: {
        map: 'china',
        roam: true, // 允许缩放和平移
        zoom: 1.2,
        center: [107, 36],
        scaleLimit: {
          min: 0.8,
          max: 3,
        },
        itemStyle: {
          areaColor: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(46, 134, 193, 0.8)' },
              { offset: 1, color: 'rgba(11, 36, 59, 0.9)' },
            ],
          },
          borderColor: 'rgba(255, 255, 255, 0.3)',
          borderWidth: 1,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
          shadowBlur: 10,
        },
        emphasis: {
          itemStyle: {
            areaColor: 'rgba(46, 134, 193, 1)',
            borderColor: 'rgba(255, 255, 255, 0.8)',
            borderWidth: 2,
          },
        },
      },
      series: [
        {
          type: 'scatter',
          coordinateSystem: 'geo',
          data: mapData,
          symbolSize: function (val) {
            return Math.max(val[2] / 4, 15);
          },
          symbol: 'circle',
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(255, 255, 255, 0.5)',
          },
          emphasis: {
            scale: 1.5,
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'rgba(255, 255, 255, 0.8)',
            },
          },
        },
        {
          type: 'effectScatter',
          coordinateSystem: 'geo',
          data: mapData,
          symbolSize: function (val) {
            return Math.max(val[2] / 6, 10);
          },
          showEffectOn: 'render',
          rippleEffect: {
            brushType: 'stroke',
            scale: 2.5,
            period: 4,
          },
          itemStyle: {
            color: 'rgba(255, 255, 255, 0.8)',
            shadowBlur: 10,
            shadowColor: 'rgba(255, 255, 255, 0.5)',
          },
        },
      ],
    };
    setMapOptions(option);
  }

  // 加载中国地图数据
  async function loadChinaMapData() {
    try {
      // 直接从 public 目录加载地图数据
      const response = await fetch('/json/100000_full.json');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const geoJson = await response.json();
      console.log('地图数据加载成功');

      // 注册地图数据到 ECharts
      echarts.registerMap('china', geoJson);

      // 初始化地图
      initMap();

      // 添加地图点击事件
      setTimeout(() => {
        const chartInstance = getInstance();
        if (chartInstance) {
          chartInstance.on('click', handleMapClick);
        }
      }, 200);
    } catch (error) {
      console.error('加载中国地图数据失败:', error);
      // 如果加载失败，显示错误提示并尝试备用方案
      console.warn('地图数据加载失败，尝试使用内置地图数据');
      initMapWithoutGeoData();
    }
  }

  // 备用方案：不使用地理数据的地图初始化
  function initMapWithoutGeoData() {
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: { color: '#fff' },
        formatter: function (params) {
          return `${params.name}<br/>活跃度: ${params.value}`;
        },
      },
      series: [
        {
          type: 'scatter',
          coordinateSystem: 'cartesian2d',
          data: mapData.map((item) => ({
            name: item.name,
            value: [item.value[0] - 100, item.value[1] - 30, item.value[2]],
            itemStyle: item.itemStyle,
          })),
          symbolSize: function (val) {
            return Math.max(val[2] / 4, 15);
          },
          symbol: 'circle',
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(255, 255, 255, 0.5)',
          },
        },
      ],
      xAxis: {
        show: false,
        min: 0,
        max: 20,
      },
      yAxis: {
        show: false,
        min: 0,
        max: 15,
      },
      grid: {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
      },
    };
    setMapOptions(option);
  }

  // 处理地图点击事件
  function handleMapClick(params) {
    if (params.seriesType === 'scatter') {
      const locationName = params.name;
      console.log(`点击了位置: ${locationName}`);
      // 这里可以添加弹窗显示详细信息的逻辑
      // 例如：showLocationDetail(locationName);
    }
  }

  onMounted(() => {
    setTimeout(() => {
      loadChinaMapData();
    }, 100);
  });
</script>

<style scoped>
  /* 响应式设计 */
  @media (max-width: 1400px) {
    .metrics-grid {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 12px;
    }

    .metrics-container {
      height: 180px;
    }

    .metric-content {
      padding: 15px;
    }

    .metric-value {
      font-size: 28px;
    }
  }

  @media (max-width: 1200px) {
    .map-chart {
      min-height: 250px;
    }

    .metrics-grid {
      grid-template-columns: repeat(4, 1fr);
      grid-template-rows: 1fr;
    }

    .metrics-container {
      height: 120px;
    }

    .metric-content {
      padding: 12px;
    }

    .metric-title {
      font-size: 12px;
    }

    .metric-value {
      font-size: 24px;
    }
  }

  @media (max-width: 768px) {
    .metrics-grid {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
    }

    .metrics-container {
      height: 160px;
    }
  }

  .center-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
  }

  .map-container {
    flex: 1;
    overflow: hidden;

    /* border: 1px solid rgba(255, 255, 255, 10%);
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(11, 36, 59, 90%) 0%, rgba(46, 134, 193, 60%) 100%);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 30%);
    backdrop-filter: blur(10px); */
  }

  .map-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 10%);
  }

  .map-title {
    margin: 0;
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 50%);
  }

  .map-chart {
    height: calc(100% - 80px);
    min-height: 300px;
  }

  .metrics-container {
    flex-shrink: 0;
    height: 140px;
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    height: 100%;
  }

  .metric-card {
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 10%);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 30%);
    cursor: pointer;
    backdrop-filter: blur(10px);
  }

  .metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 40%);
  }

  .metric-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    padding: 20px;
  }

  .metric-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .metric-title {
    color: rgba(255, 255, 255, 90%);
    font-size: 14px;
    font-weight: 500;
    line-height: 1.2;
  }

  .metric-icon {
    color: rgba(255, 255, 255, 80%);
  }

  .metric-value {
    margin: 8px 0;
    color: #fff;
    font-size: 32px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 50%);
  }

  .metric-change {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 500;
  }
</style>
