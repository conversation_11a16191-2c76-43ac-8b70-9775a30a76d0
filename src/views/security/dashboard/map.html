<!DOCTYPE html><html lang="zh-CN"><head><meta name="x-poe-datastore-behavior" content="local_only"><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D中国地图组件</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts-gl@2.0.9/dist/echarts-gl.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0c1426 100%);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }
        
        .container {
            width: 100vw;
            height: 100vh;
            position: relative;
            background: 
                radial-gradient(circle at 20% 20%, rgba(64, 224, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 100, 150, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0c1426 100%);
        }
        
        .grid-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(64, 224, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(64, 224, 255, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            pointer-events: none;
            opacity: 0.3;
        }
        
        #map-container {
            width: 100%;
            height: 100%;
            position: relative;
            z-index: 10;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #40e0ff;
            font-size: 18px;
            z-index: 100;
        }
        
        .city-tooltip {
            position: absolute;
            background: rgba(0, 20, 40, 0.95);
            border: 1px solid #40e0ff;
            border-radius: 8px;
            padding: 12px;
            color: #fff;
            font-size: 14px;
            pointer-events: none;
            z-index: 1000;
            box-shadow: 0 8px 32px rgba(64, 224, 255, 0.3);
            backdrop-filter: blur(10px);
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
<script src="https://puc.poecdn.net/authenticated_preview_page/syncedState.bd4eeeb8e8e02052ee92.js"></script></head>
<body>
    <div class="container">
        <div class="grid-bg"></div>
        <div class="loading" id="loading">正在加载地图数据...</div>
        <div id="map-container"></div>
    </div>

    <script>
        // 城市数据
        const cityData = [
            {
                name: '成都',
                coord: [104.066, 30.572],
                value: 100,
                status: 'online',
                info: '成都数据中心<br/>状态：正常运行<br/>节点数：156'
            },
            {
                name: '石家庄',
                coord: [114.514, 38.042],
                value: 80,
                status: 'online',
                info: '石家庄数据中心<br/>状态：正常运行<br/>节点数：89'
            },
            {
                name: '上海',
                coord: [121.473, 31.230],
                value: 60,
                status: 'building',
                info: '上海数据中心<br/>状态：建设中<br/>预计完成：2024年Q2'
            },
            {
                name: '北京',
                coord: [116.407, 39.904],
                value: 120,
                status: 'online',
                info: '北京数据中心<br/>状态：正常运行<br/>节点数：203'
            },
            {
                name: '深圳',
                coord: [114.057, 22.543],
                value: 90,
                status: 'online',
                info: '深圳数据中心<br/>状态：正常运行<br/>节点数：134'
            },
            {
                name: '西安',
                coord: [108.939, 34.341],
                value: 40,
                status: 'building',
                info: '西安数据中心<br/>状态：建设中<br/>预计完成：2024年Q3'
            }
        ];

        let myChart;
        
        // 初始化图表
        function initChart() {
            const container = document.getElementById('map-container');
            myChart = echarts.init(container);
            
            // 注册中国地图
            fetch('https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json')
                .then(response => response.json())
                .then(geoJson => {
                    echarts.registerMap('china', geoJson);
                    renderChart();
                    document.getElementById('loading').style.display = 'none';
                })
                .catch(error => {
                    console.error('加载地图数据失败:', error);
                    document.getElementById('loading').innerHTML = '地图数据加载失败';
                });
        }

        // 渲染图表
        function renderChart() {
            const option = {
                backgroundColor: 'transparent',
                globe: {
                    show: false
                },
                geo3D: {
                    map: 'china',
                    roam: true,
                    itemStyle: {
                        color: '#1e3c72',
                        opacity: 0.8,
                        borderWidth: 2,
                        borderColor: '#40e0ff'
                    },
                    emphasis: {
                        itemStyle: {
                            color: '#2a5298',
                            borderColor: '#80ff80',
                            borderWidth: 3
                        }
                    },
                    light: {
                        main: {
                            color: '#ffffff',
                            intensity: 1.2,
                            shadow: true,
                            shadowQuality: 'high',
                            alpha: 30,
                            beta: 40
                        },
                        ambient: {
                            color: '#40e0ff',
                            intensity: 0.4
                        }
                    },
                    viewControl: {
                        autoRotate: false,
                        distance: 120,
                        alpha: 30,
                        beta: 0,
                        center: [0, 0, 0],
                        animation: true,
                        animationDurationUpdate: 1000,
                        damping: 0.8,
                        rotateSensitivity: 1,
                        zoomSensitivity: 1,
                        panSensitivity: 1
                    },
                    regionHeight: 8,
                    groundPlane: {
                        show: true,
                        color: '#0a1428'
                    },
                    environment: '#0a1428',
                    postEffect: {
                        enable: true,
                        bloom: {
                            enable: true,
                            intensity: 0.3
                        },
                        SSAO: {
                            enable: true,
                            radius: 2
                        }
                    }
                },
                series: [
                    // 3D地图
                    {
                        type: 'map3D',
                        map: 'china',
                        regionHeight: 8,
                        itemStyle: {
                            color: function(params) {
                                return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    {offset: 0, color: '#2563eb'},
                                    {offset: 1, color: '#1e40af'}
                                ]);
                            },
                            opacity: 0.85,
                            borderWidth: 2,
                            borderColor: '#40e0ff',
                            borderType: 'solid'
                        },
                        emphasis: {
                            itemStyle: {
                                color: '#3b82f6',
                                borderColor: '#80ff80',
                                borderWidth: 3
                            }
                        },
                        light: {
                            main: {
                                color: '#ffffff',
                                intensity: 1.5,
                                shadow: true,
                                shadowQuality: 'high',
                                alpha: 25,
                                beta: 45
                            },
                            ambient: {
                                color: '#40e0ff',
                                intensity: 0.5
                            }
                        }
                    },
                    // 城市标注点
                    {
                        type: 'scatter3D',
                        coordinateSystem: 'geo3D',
                        data: cityData.map(city => ({
                            name: city.name,
                            value: [...city.coord, 15],
                            itemStyle: {
                                color: city.status === 'online' ? '#40e0ff' : '#ff6b35',
                                opacity: 0.9
                            },
                            status: city.status,
                            info: city.info
                        })),
                        symbol: 'circle',
                        symbolSize: function(val, params) {
                            return params.data.status === 'online' ? 15 : 12;
                        },
                        itemStyle: {
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        },
                        label: {
                            show: true,
                            position: 'top',
                            color: '#ffffff',
                            fontSize: 12,
                            fontWeight: 'bold',
                            backgroundColor: 'rgba(0,0,0,0.6)',
                            padding: [4, 8],
                            borderRadius: 4,
                            borderWidth: 1,
                            borderColor: function(params) {
                                return params.data.status === 'online' ? '#40e0ff' : '#ff6b35';
                            }
                        },
                        emphasis: {
                            itemStyle: {
                                opacity: 1,
                                borderWidth: 3
                            },
                            label: {
                                show: true,
                                fontSize: 14
                            }
                        }
                    }
                ],
                animation: true,
                animationDuration: 2000,
                animationEasing: 'cubicOut'
            };

            myChart.setOption(option);

            // 添加点击事件
            myChart.on('click', function(params) {
                if (params.seriesType === 'scatter3D') {
                    const cityInfo = cityData.find(city => city.name === params.name);
                    if (cityInfo) {
                        showCityTooltip(params.event.offsetX, params.event.offsetY, cityInfo);
                    }
                }
            });

            // 添加鼠标悬停事件
            myChart.on('mouseover', function(params) {
                if (params.seriesType === 'scatter3D') {
                    const cityInfo = cityData.find(city => city.name === params.name);
                    if (cityInfo) {
                        showCityTooltip(params.event.offsetX, params.event.offsetY, cityInfo);
                    }
                }
            });

            myChart.on('mouseout', function(params) {
                hideCityTooltip();
            });

            // 启动动画
            startAnimation();
        }

        // 显示城市信息提示框
        function showCityTooltip(x, y, cityInfo) {
            let tooltip = document.querySelector('.city-tooltip');
            if (!tooltip) {
                tooltip = document.createElement('div');
                tooltip.className = 'city-tooltip';
                document.body.appendChild(tooltip);
            }
            
            tooltip.innerHTML = `
                <div style="font-weight: bold; color: ${cityInfo.status === 'online' ? '#40e0ff' : '#ff6b35'}; margin-bottom: 8px;">
                    ${cityInfo.name}
                </div>
                <div style="line-height: 1.5;">
                    ${cityInfo.info}
                </div>
            `;
            
            tooltip.style.left = (x + 15) + 'px';
            tooltip.style.top = (y - 10) + 'px';
            tooltip.style.display = 'block';
        }

        // 隐藏提示框
        function hideCityTooltip() {
            const tooltip = document.querySelector('.city-tooltip');
            if (tooltip) {
                tooltip.style.display = 'none';
            }
        }

        // 启动入场动画
        function startAnimation() {
            // 初始隐藏地图
            myChart.setOption({
                geo3D: {
                    regionHeight: 0
                }
            });

            // 动画显示地图
            setTimeout(() => {
                myChart.setOption({
                    geo3D: {
                        regionHeight: 8
                    },
                    series: [{
                        animationDuration: 2000,
                        animationEasing: 'elasticOut'
                    }]
                });
            }, 500);

            // 延迟显示城市标注
            setTimeout(() => {
                myChart.setOption({
                    series: [
                        {},
                        {
                            animationDuration: 1500,
                            animationDelay: function(idx) {
                                return idx * 200;
                            }
                        }
                    ]
                });
            }, 1500);
        }

        // 响应式处理
        window.addEventListener('resize', function() {
            if (myChart) {
                myChart.resize();
            }
        });

        // 支持深色模式
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
        });
    </script>


</body></html>