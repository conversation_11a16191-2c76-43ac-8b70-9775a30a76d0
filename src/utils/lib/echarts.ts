import * as echarts from 'echarts/core';
// 导入 echarts-gl 以支持 3D 图表
import 'echarts-gl';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON>ctorialBar<PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON>atter<PERSON><PERSON>,
  EffectScatter<PERSON>hart,
  <PERSON>auge<PERSON>hart,
} from 'echarts/charts';

import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  PolarComponent,
  AriaComponent,
  ParallelComponent,
  LegendComponent,
  RadarComponent,
  ToolboxComponent,
  DataZoomComponent,
  VisualMapComponent,
  TimelineComponent,
  CalendarComponent,
  GraphicComponent,
  GeoComponent,
} from 'echarts/components';

import { SVGRenderer } from 'echarts/renderers';

echarts.use([
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  PolarComponent,
  AriaComponent,
  ParallelComponent,
  GeoComponent,
  <PERSON><PERSON>hart,
  <PERSON>Chart,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Radar<PERSON>hart,
  SVGRenderer,
  PictorialBar<PERSON>hart,
  RadarComponent,
  ToolboxComponent,
  DataZoomComponent,
  VisualMap<PERSON>omponent,
  TimelineComponent,
  CalendarComponent,
  GraphicComponent,
  <PERSON>atter<PERSON>hart,
  EffectScatterChart,
  GaugeChart,
]);

export default echarts;
